@echo off
REM OpenCode spouštěcí skript s nastaveným PATH
REM Přidá Go, Ripgrep a FZF do PATH a spustí OpenCode

echo Nastavuji prostředí pro OpenCode...

REM Nastavení Go
set GOROOT=C:\Go\go
set GOPATH=J:\go-workspace
set PATH=C:\Go\go\bin;%PATH%

REM Přidání nás<PERSON>jů (Ripgrep a FZF)
set PATH=J:\tools;%PATH%

REM Ověření dostupnosti nástrojů
echo Kontroluji dostupnost nástrojů...
go version
rg --version
fzf --version

echo.
echo OpenCode je připraven k použití!
echo.
echo Použití:
echo   opencode              - Interaktivní režim
echo   opencode --help       - Zobrazí nápovědu
echo   opencode -p "dotaz"   - Jednorázový dotaz
echo.

REM Spuštění OpenCode s předanými argumenty
opencode.exe %*
